{"name": "web-driver", "version": "0.1.0", "private": true, "scripts": {"dev": "pnpm exec next dev --turbopack --port 3003", "build": "pnpm run patch-styled-jsx && pnpm exec next build", "start": "pnpm exec next start --port 3003", "lint": "pnpm exec next lint", "type-check": "pnpm exec tsc --noEmit", "clean": "rm -rf .next", "patch-styled-jsx": "node scripts/patch-styled-jsx.js", "postinstall": "pnpm run patch-styled-jsx"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.50.0", "@types/react": "~19.0.10", "@types/react-dom": "^19.1.0", "@upstash/redis": "^1.35.0", "api-client": "workspace:*", "axios": "^1.9.0", "business-logic": "workspace:*", "config": "workspace:*", "firebase": "^10.13.1", "firebase-admin": "^12.7.0", "firebase-config": "workspace:*", "lucide-react": "^0.445.0", "next": "15.3.2", "react": "19.0.0", "react-dom": "19.0.0", "shared-types": "workspace:*", "shared-ui": "workspace:*", "shared-utils": "workspace:*"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.47", "tailwindcss": "^4", "typescript": "^5"}}